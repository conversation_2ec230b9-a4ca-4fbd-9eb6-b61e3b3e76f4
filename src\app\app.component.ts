import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  styleUrl: './app.component.scss',
})
export class AppComponent {
  constructor(private translate: TranslateService) {
    localStorage.setItem('webSiteId', 'C52CD7E3-2EFB-478E-BEAB-CBF98DB8309D');
    sessionStorage.setItem('webSiteId', 'C52CD7E3-2EFB-478E-BEAB-CBF98DB8309D');

    this.translate.setDefaultLang('zh');
  }

  fontSize: number = sessionStorage.getItem('fontSize')
    ? parseFloat(sessionStorage.getItem('fontSize') as string)
    : 1;

  changeFontSize(fontSize: number) {
    this.fontSize = fontSize;
  }
}
